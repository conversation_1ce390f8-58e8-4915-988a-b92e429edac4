<?php
/**
 * Database Configuration for CyberWolf Labs
 * Update these settings for your hosting environment
 */

// Hosting Database Configuration
// Update these values with your hosting provider's database details
$db_config = [
    'host' => 'localhost',  // Usually 'localhost' for shared hosting
    'username' => 'cyberwolf_user',  // Your cPanel/hosting database username
    'password' => 'your_secure_password',  // Your database password
    'database' => 'cyberwolf_labs',  // Your database name
    'charset' => 'utf8mb4'
];

// Alternative configuration for different hosting providers
$hosting_configs = [
    'shared_hosting' => [
        'host' => 'localhost',
        'username' => 'cyberwolf_user',
        'password' => 'your_password',
        'database' => 'cyberwolf_labs'
    ],
    'vps_hosting' => [
        'host' => 'localhost',
        'username' => 'root',
        'password' => 'your_root_password',
        'database' => 'cyberwolf_labs'
    ],
    'cloud_hosting' => [
        'host' => 'your-db-host.com',
        'username' => 'cyberwolf_user',
        'password' => 'your_cloud_password',
        'database' => 'cyberwolf_labs'
    ]
];

/**
 * Get database connection
 */
function getDatabaseConnection() {
    global $db_config;
    
    $conn = @mysqli_connect(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    if (!$conn) {
        // Log error for debugging
        error_log("Database connection failed: " . mysqli_connect_error());
        return false;
    }
    
    // Set charset
    mysqli_set_charset($conn, $db_config['charset']);
    
    return $conn;
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    $conn = getDatabaseConnection();
    
    if (!$conn) {
        return [
            'success' => false,
            'error' => mysqli_connect_error(),
            'message' => 'Failed to connect to database'
        ];
    }
    
    // Test if tables exist
    $tables = ['users', 'users_csrf', 'comments', 'books', 'contact'];
    $existing_tables = [];
    $missing_tables = [];
    
    foreach ($tables as $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($result) > 0) {
            $existing_tables[] = $table;
        } else {
            $missing_tables[] = $table;
        }
    }
    
    mysqli_close($conn);
    
    return [
        'success' => true,
        'existing_tables' => $existing_tables,
        'missing_tables' => $missing_tables,
        'message' => 'Database connection successful'
    ];
}

/**
 * Create database tables for hosting environment
 */
function createDatabaseTables() {
    $conn = getDatabaseConnection();
    
    if (!$conn) {
        return false;
    }
    
    $sql_commands = [
        // Users table for SQL injection lab
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            pass VARCHAR(100) NOT NULL
        )",
        
        // Users table for CSRF lab
        "CREATE TABLE IF NOT EXISTS users_csrf (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            password VARCHAR(100) NOT NULL
        )",
        
        // Comments table
        "CREATE TABLE IF NOT EXISTS comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            uname VARCHAR(50) NOT NULL,
            comment TEXT NOT NULL,
            link VARCHAR(255) DEFAULT '#'
        )",
        
        // Books table
        "CREATE TABLE IF NOT EXISTS books (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            auther VARCHAR(100) NOT NULL,
            story TEXT
        )",
        
        // Contact table for XSS lab
        "CREATE TABLE IF NOT EXISTS contact (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            message TEXT NOT NULL,
            user_agent TEXT
        )"
    ];
    
    $insert_commands = [
        // Insert users
        "INSERT IGNORE INTO users (username, pass) VALUES 
         ('guest', 'guest'), ('admin', 'admin123'), ('user1', 'password1')",
        
        // Insert CSRF users
        "INSERT IGNORE INTO users_csrf (username, email, password) VALUES 
         ('guest', '<EMAIL>', 'guest'), 
         ('admin', '<EMAIL>', 'admin123')",
        
        // Insert comments
        "INSERT IGNORE INTO comments (uname, comment, link) VALUES 
         ('admin', 'Welcome to CyberWolf Security Labs!', '#'),
         ('guest', 'This is a test comment for demonstration.', '#')",
        
        // Insert books
        "INSERT IGNORE INTO books (name, auther, story) VALUES 
         ('Web Security Fundamentals', 'CyberWolf Team', 'Complete guide to web security testing.'),
         ('Ethical Hacking Handbook', 'Security Expert', 'Learn ethical hacking techniques.'),
         ('SQL Injection Mastery', 'Database Security', 'Deep dive into SQL injection attacks.')",
        
        // Insert contact entries
        "INSERT IGNORE INTO contact (name, email, message, user_agent) VALUES 
         ('Test User', '<EMAIL>', 'Test message for contact form.', 'Mozilla/5.0'),
         ('Security Tester', '<EMAIL>', 'Testing contact functionality.', 'Mozilla/5.0')"
    ];
    
    // Execute table creation
    foreach ($sql_commands as $sql) {
        if (!mysqli_query($conn, $sql)) {
            error_log("Failed to create table: " . mysqli_error($conn));
            mysqli_close($conn);
            return false;
        }
    }
    
    // Execute data insertion
    foreach ($insert_commands as $sql) {
        if (!mysqli_query($conn, $sql)) {
            error_log("Failed to insert data: " . mysqli_error($conn));
        }
    }
    
    mysqli_close($conn);
    return true;
}

// Auto-setup if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'db_config.php') {
    header('Content-Type: text/plain');
    
    echo "🗄️  CyberWolf Labs - Database Configuration\n";
    echo "==========================================\n\n";
    
    // Test connection
    $test_result = testDatabaseConnection();
    
    if ($test_result['success']) {
        echo "✅ " . $test_result['message'] . "\n\n";
        
        echo "📊 Existing tables: " . implode(', ', $test_result['existing_tables']) . "\n";
        
        if (!empty($test_result['missing_tables'])) {
            echo "❌ Missing tables: " . implode(', ', $test_result['missing_tables']) . "\n";
            echo "🔧 Creating missing tables...\n";
            
            if (createDatabaseTables()) {
                echo "✅ Database tables created successfully!\n";
            } else {
                echo "❌ Failed to create database tables\n";
            }
        } else {
            echo "✅ All required tables exist\n";
        }
    } else {
        echo "❌ " . $test_result['message'] . "\n";
        echo "🔧 Error: " . $test_result['error'] . "\n\n";
        
        echo "💡 Please update the database configuration in db_config.php:\n";
        echo "   - host: Your database host (usually 'localhost')\n";
        echo "   - username: Your database username\n";
        echo "   - password: Your database password\n";
        echo "   - database: Your database name\n";
    }
    
    echo "\n🌐 Domain: cyberwolf.wuaze.com\n";
    echo "🔗 Labs URL: https://cyberwolf.wuaze.com/labs/\n";
}
?>
