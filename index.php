<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Side Hacking Labs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .cyberwolf {
            font-size: 1.1em;
            color: #4ecdc4;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .labs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .lab-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .lab-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }

        .lab-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #4ecdc4;
        }

        .lab-card p {
            margin-bottom: 20px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .lab-button {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1em;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .lab-button:hover {
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .vulnerability-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .footer {
            text-align: center;
            margin-top: 60px;
            padding: 30px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer p {
            opacity: 0.7;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .labs-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .lab-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Server Side Hacking</h1>
            <p>Comprehensive Web Application Security Testing Labs</p>
            <p class="cyberwolf">Conducted by CyberWolf</p>
        </div>

        <div class="labs-grid">
            <div class="lab-card">
                <span class="vulnerability-icon">💉</span>
                <h3>SQL Injection</h3>
                <p>Learn to exploit database vulnerabilities through malicious SQL queries and bypass authentication mechanisms.</p>
                <a href="sql/" class="lab-button">Start SQL Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">🔥</span>
                <h3>Cross-Site Scripting (XSS)</h3>
                <p>Master reflected, stored, and DOM-based XSS attacks with advanced filter bypass techniques.</p>
                <a href="xss/" class="lab-button">Start XSS Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">🎭</span>
                <h3>Cross-Site Request Forgery</h3>
                <p>Exploit CSRF vulnerabilities to perform unauthorized actions on behalf of authenticated users.</p>
                <a href="csrf/" class="lab-button">Start CSRF Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">🔑</span>
                <h3>Insecure Direct Object Reference</h3>
                <p>Discover and exploit IDOR vulnerabilities to access unauthorized resources and escalate privileges.</p>
                <a href="idor/" class="lab-button">Start IDOR Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">📁</span>
                <h3>File Upload Vulnerabilities</h3>
                <p>Bypass file upload restrictions and achieve remote code execution through malicious file uploads.</p>
                <a href="upload/" class="lab-button">Start Upload Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">📂</span>
                <h3>Local File Inclusion</h3>
                <p>Exploit LFI vulnerabilities to read sensitive files and achieve remote code execution via log poisoning.</p>
                <a href="file inclusion/" class="lab-button">Start LFI Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">🔍</span>
                <h3>XML External Entity (XXE)</h3>
                <p>Learn to exploit XML parsers to read local files and perform server-side request forgery attacks.</p>
                <a href="xxe/" class="lab-button">Start XXE Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">🌐</span>
                <h3>CORS Misconfiguration</h3>
                <p>Exploit Cross-Origin Resource Sharing misconfigurations to steal sensitive data across domains.</p>
                <a href="cors/" class="lab-button">Start CORS Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">📦</span>
                <h3>Insecure Deserialization</h3>
                <p>Exploit deserialization vulnerabilities to achieve remote code execution and authentication bypass.</p>
                <a href="insecuredes/" class="lab-button">Start Deserialization Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">📡</span>
                <h3>JSONP Vulnerabilities</h3>
                <p>Exploit JSONP endpoints to extract sensitive data through callback manipulation techniques.</p>
                <a href="jsonp/" class="lab-button">Start JSONP Lab</a>
            </div>

            <div class="lab-card">
                <span class="vulnerability-icon">💬</span>
                <h3>PostMessage Vulnerabilities</h3>
                <p>Exploit HTML5 postMessage API vulnerabilities to perform cross-frame scripting attacks.</p>
                <a href="postmessage/" class="lab-button">Start PostMessage Lab</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 CyberWolf Security Labs | Educational Purpose Only</p>
            <p>Practice ethical hacking in a controlled environment</p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.lab-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animation to buttons
            const buttons = document.querySelectorAll('.lab-button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
    </script>

    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</body>
</html>
